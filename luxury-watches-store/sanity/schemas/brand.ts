import { defineField, defineType } from 'sanity'

export const brand = defineType({
  name: 'brand',
  title: 'Brand',
  type: 'document',
  fields: [
    defineField({
      name: 'name',
      title: 'Brand Name',
      type: 'string',
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'name',
        maxLength: 96,
      },
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'logo',
      title: 'Brand Logo',
      type: 'image',
      options: {
        hotspot: true,
      }
    }),
    defineField({
      name: 'description',
      title: 'Description',
      type: 'text',
      rows: 4
    }),
    defineField({
      name: 'founded',
      title: 'Founded Year',
      type: 'number'
    }),
    defineField({
      name: 'country',
      title: 'Country of Origin',
      type: 'string'
    }),
    defineField({
      name: 'website',
      title: 'Official Website',
      type: 'url'
    }),
    defineField({
      name: 'heritage',
      title: 'Brand Heritage',
      type: 'array',
      of: [{ type: 'block' }]
    }),
    defineField({
      name: 'isLuxury',
      title: 'Luxury Brand',
      type: 'boolean',
      initialValue: true
    }),
    defineField({
      name: 'priceRange',
      title: 'Price Range',
      type: 'object',
      fields: [
        { name: 'min', title: 'Minimum Price', type: 'number' },
        { name: 'max', title: 'Maximum Price', type: 'number' },
      ]
    }),
    defineField({
      name: 'seo',
      title: 'SEO',
      type: 'object',
      fields: [
        { name: 'title', title: 'SEO Title', type: 'string' },
        { name: 'description', title: 'SEO Description', type: 'text', rows: 3 },
      ]
    }),
  ],
  preview: {
    select: {
      title: 'name',
      media: 'logo',
      country: 'country',
      founded: 'founded'
    },
    prepare(selection) {
      const { title, country, founded } = selection
      return {
        title,
        subtitle: `${country} ${founded ? `(${founded})` : ''}`
      }
    }
  }
})
