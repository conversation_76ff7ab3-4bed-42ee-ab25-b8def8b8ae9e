'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  Search,
  User,
  Heart,
  Menu,
  X
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Search as SearchComponent } from '@/components/ui/search';
import { CartIcon } from '@/components/cart/cart-icon';
import { ComparisonIcon } from '@/components/product/comparison-icon';
import { LuxuryNavigation } from '@/components/navigation/luxury-navigation';
import { MobileMegaMenu } from '@/components/navigation/mobile-mega-menu';
import { useAuth } from '@/contexts/auth-context';
import { useWishlist } from '@/contexts/wishlist-context';

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const { isAuthenticated, user } = useAuth();
  const { itemCount: wishlistCount } = useWishlist();

  // Close menus when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;

      // Don't close if clicking on search button or search input
      if (target.closest('[data-search-toggle]') || target.closest('[data-search-input]')) {
        return;
      }

      // Don't close if clicking on mobile menu button
      if (target.closest('[data-mobile-menu-toggle]')) {
        return;
      }

      // Don't close if clicking on brands dropdown
      if (target.closest('[data-brands-dropdown]')) {
        return;
      }

      setIsMenuOpen(false);
      setIsSearchOpen(false);
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  return (
    <header className="sticky top-0 z-50 bg-white/95 backdrop-blur-md border-b border-gray-200">
      <div className="container mx-auto px-4">
        {/* Top Bar */}
        <div className="hidden md:flex items-center justify-between py-2 text-sm text-gray-600 border-b border-gray-100">
          <div className="flex items-center space-x-6">
            <span>Free shipping on orders over $1,000</span>
            <span>•</span>
            <span>Authentic luxury watches</span>
          </div>
          <div className="flex items-center space-x-4">
            <Link href="/contact" className="hover:text-luxury-gold transition-colors">
              Contact: +****************
            </Link>
          </div>
        </div>

        {/* Main Header */}
        <div className="flex items-center justify-between py-4">
          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            data-mobile-menu-toggle
          >
            {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </Button>

          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-luxury-gold rounded-full flex items-center justify-center">
              <span className="text-luxury-black font-bold text-sm">LT</span>
            </div>
            <span className="font-luxury-serif text-2xl font-bold text-luxury-black hidden sm:block">
              Luxury Watches
            </span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:block">
            <LuxuryNavigation />
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-4">
            {/* Search */}
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsSearchOpen(!isSearchOpen)}
              className="text-luxury-black hover:text-luxury-gold"
              data-search-toggle
            >
              <Search className="h-5 w-5" />
            </Button>

            {/* Wishlist */}
            <Link href={isAuthenticated ? "/account/wishlist" : "/auth/login"}>
              <Button
                variant="ghost"
                size="icon"
                className="text-luxury-black hover:text-luxury-gold relative"
              >
                <Heart className="h-5 w-5" />
                {wishlistCount > 0 && (
                  <span className="absolute -top-1 -right-1 bg-luxury-gold text-luxury-black text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium">
                    {wishlistCount > 99 ? '99+' : wishlistCount}
                  </span>
                )}
              </Button>
            </Link>

            {/* Comparison */}
            <ComparisonIcon />

            {/* Account / Auth */}
            {isAuthenticated ? (
              <Link href="/account">
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-luxury-black hover:text-luxury-gold relative"
                  title={`${user?.firstName} ${user?.lastName}`}
                >
                  {user?.avatar ? (
                    <img
                      src={user.avatar}
                      alt={`${user.firstName} ${user.lastName}`}
                      className="w-5 h-5 rounded-full object-cover"
                    />
                  ) : (
                    <User className="h-5 w-5" />
                  )}
                  <span className="absolute -bottom-1 -right-1 w-2 h-2 bg-green-500 rounded-full border border-white"></span>
                </Button>
              </Link>
            ) : (
              <div className="flex items-center space-x-2">
                <Link href="/auth/login">
                  <Button variant="ghost" size="sm" className="text-luxury-black hover:text-luxury-gold">
                    Sign In
                  </Button>
                </Link>
                <Link href="/auth/register">
                  <Button variant="luxury" size="sm">
                    Sign Up
                  </Button>
                </Link>
              </div>
            )}

            {/* Cart */}
            <CartIcon />
          </div>
        </div>

        {/* Enhanced Search Component */}
        <SearchComponent
          isOpen={isSearchOpen}
          onClose={() => setIsSearchOpen(false)}
        />
      </div>

      {/* Mobile Mega Menu */}
      <MobileMegaMenu
        isOpen={isMenuOpen}
        onClose={() => setIsMenuOpen(false)}
      />
    </header>
  );
}
