'use client';

import { Fragment } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Plus, Minus, ShoppingBag, Trash2 } from 'lucide-react';
import { useCart } from '@/contexts/cart-context';
import { Button } from '@/components/ui/button';
import { formatPrice, cn } from '@/lib/utils';

export function CartSidebar() {
  const { 
    items, 
    isOpen, 
    total, 
    itemCount, 
    closeCart, 
    updateQuantity, 
    removeItem 
  } = useCart();

  const handleQuantityChange = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeItem(itemId);
    } else {
      updateQuantity(itemId, newQuantity);
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={closeCart}
            className="fixed inset-0 bg-black bg-opacity-50 z-40"
          />

          {/* Sidebar */}
          <motion.div
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
            className="fixed right-0 top-0 h-full w-full max-w-md bg-white shadow-2xl z-50 flex flex-col"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div className="flex items-center gap-2">
                <ShoppingBag className="h-5 w-5 text-luxury-gold" />
                <h2 className="font-luxury-serif text-xl font-semibold text-luxury-black">
                  Shopping Cart
                </h2>
                {itemCount > 0 && (
                  <span className="bg-luxury-gold text-luxury-black text-sm font-semibold px-2 py-1 rounded-full">
                    {itemCount}
                  </span>
                )}
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={closeCart}
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            {/* Cart Items */}
            <div className="flex-1 overflow-y-auto">
              {items.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full p-6 text-center">
                  <ShoppingBag className="h-16 w-16 text-gray-300 mb-4" />
                  <h3 className="font-medium text-gray-900 mb-2">Your cart is empty</h3>
                  <p className="text-gray-500 mb-6">
                    Add some luxury timepieces to get started
                  </p>
                  <Button onClick={closeCart} asChild>
                    <Link href="/catalog">
                      Continue Shopping
                    </Link>
                  </Button>
                </div>
              ) : (
                <div className="p-6 space-y-6">
                  {items.map((item) => (
                    <motion.div
                      key={item.id}
                      layout
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      className="flex gap-4 p-4 bg-luxury-cream rounded-lg"
                    >
                      {/* Product Image */}
                      <div className="relative w-20 h-20 flex-shrink-0">
                        <Image
                          src={item.watch?.images?.[0] || '/images/placeholder-watch.svg'}
                          alt={`${item.watch?.brand || 'Watch'} ${item.watch?.name || ''}`}
                          fill
                          className="object-cover rounded-md"
                        />
                        {item.watch.isNew && (
                          <span className="absolute -top-2 -right-2 bg-luxury-gold text-luxury-black text-xs font-semibold px-2 py-1 rounded-full">
                            NEW
                          </span>
                        )}
                      </div>

                      {/* Product Details */}
                      <div className="flex-1 min-w-0">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <p className="text-xs text-luxury-gold font-medium uppercase tracking-wide">
                              {item.watch.brand}
                            </p>
                            <h3 className="font-medium text-luxury-black text-sm leading-tight">
                              {item.watch.name}
                            </h3>
                          </div>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => removeItem(item.id)}
                            className="text-gray-400 hover:text-red-500 h-8 w-8"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>

                        {/* Price */}
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            {item.watch.originalPrice && (
                              <span className="text-xs text-gray-400 line-through">
                                {formatPrice(item.watch.originalPrice)}
                              </span>
                            )}
                            <span className="font-semibold text-luxury-gold">
                              {formatPrice(item.watch.price)}
                            </span>
                          </div>

                          {/* Quantity Controls */}
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                              className="h-8 w-8 border-gray-300"
                            >
                              <Minus className="h-3 w-3" />
                            </Button>
                            <span className="w-8 text-center text-sm font-medium">
                              {item.quantity}
                            </span>
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                              className="h-8 w-8 border-gray-300"
                            >
                              <Plus className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>

                        {/* Item Total */}
                        <div className="mt-2 text-right">
                          <span className="text-sm font-semibold text-luxury-black">
                            {formatPrice(item.watch.price * item.quantity)}
                          </span>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </div>

            {/* Footer */}
            {items.length > 0 && (
              <div className="border-t border-gray-200 p-6 space-y-4">
                {/* Total */}
                <div className="flex justify-between items-center">
                  <span className="font-luxury-serif text-lg font-semibold text-luxury-black">
                    Total
                  </span>
                  <span className="font-luxury-serif text-xl font-bold text-luxury-gold">
                    {formatPrice(total)}
                  </span>
                </div>

                {/* Actions */}
                <div className="space-y-3">
                  <Button 
                    className="w-full" 
                    variant="luxury"
                    onClick={closeCart}
                    asChild
                  >
                    <Link href="/cart">
                      View Cart
                    </Link>
                  </Button>
                  <Button 
                    className="w-full" 
                    variant="outline"
                    onClick={closeCart}
                    asChild
                  >
                    <Link href="/checkout">
                      Checkout
                    </Link>
                  </Button>
                </div>

                {/* Free Shipping Notice */}
                <div className="text-center">
                  <p className="text-xs text-gray-500">
                    {total >= 1000 ? (
                      <span className="text-green-600 font-medium">
                        ✓ Free shipping included
                      </span>
                    ) : (
                      <>
                        Add {formatPrice(1000 - total)} more for free shipping
                      </>
                    )}
                  </p>
                </div>
              </div>
            )}
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
