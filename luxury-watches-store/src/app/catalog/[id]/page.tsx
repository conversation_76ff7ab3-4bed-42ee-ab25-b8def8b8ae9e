'use client';

import { useState, useEffect } from 'react';
import { notFound } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  Heart, 
  ShoppingBag, 
  Share2, 
  Star, 
  ChevronLeft, 
  ChevronRight,
  Truck,
  Shield,
  RotateCcw,
  Award
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { WatchCard } from '@/components/product/watch-card';
import { ImageGallery } from '@/components/product/image-gallery';
import { useCart } from '@/contexts/cart-context';
import { useWishlist } from '@/contexts/wishlist-context';
import { formatPrice, cn } from '@/lib/utils';
import { getAllProducts } from '@/lib/products';
import { getProductById } from '@/lib/sanity/utils';
import { ProductStructuredData, BreadcrumbStructuredData } from '@/components/seo/structured-data';
import { trackViewItem } from '@/components/analytics/google-analytics';



const getRelatedWatches = (currentWatchId: string) => {
  const allProducts = getAllProducts();
  return allProducts.filter(product => product.id !== currentWatchId).slice(0, 3);
};

interface ProductPageProps {
  params: {
    id: string;
  };
}

export default function ProductPage({ params }: ProductPageProps) {
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [activeTab, setActiveTab] = useState('description');
  const { addItem, openCart } = useCart();
  const { addItem: addToWishlist, removeItem: removeFromWishlist, isInWishlist } = useWishlist();
  const [watchId, setWatchId] = useState<string>('');
  const [watch, setWatch] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadProduct = async () => {
      try {
        const resolvedParams = await params;
        setWatchId(resolvedParams.id);

        const product = await getProductById(resolvedParams.id);
        if (product) {
          // Transform SimpleWatch to Product format
          const transformedProduct = {
            ...product,
            model: product.name,
            specifications: {
              'Brand': product.brand,
              'Model': product.name,
              'Category': product.category,
              'Price': `$${product.price.toLocaleString()}`
            },
            rating: 4.8,
            reviewCount: 127
          };
          setWatch(transformedProduct);
        }
      } catch (error) {
        console.error('Error loading product:', error);
      } finally {
        setLoading(false);
      }
    };

    loadProduct();
  }, [params]);

  // Show loading state while product is being loaded
  if (loading || !watch) {
    return (
      <div className="min-h-screen bg-luxury-white flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-luxury-gold border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading watch details...</p>
        </div>
      </div>
    );
  }

  const handleAddToCart = () => {
    addItem(watch, quantity);
    openCart();
  };

  const handleWishlistToggle = () => {
    if (isInWishlist(watch.id)) {
      removeFromWishlist(watch.id);
    } else {
      addToWishlist(watch);
    }
  };

  const nextImage = () => {
    setSelectedImageIndex((prev) => 
      prev === watch.images.length - 1 ? 0 : prev + 1
    );
  };

  const prevImage = () => {
    setSelectedImageIndex((prev) => 
      prev === 0 ? watch.images.length - 1 : prev - 1
    );
  };

  // Breadcrumb data for structured data
  const breadcrumbItems = [
    { name: 'Home', url: '/' },
    { name: 'Catalog', url: '/catalog' },
    { name: watch.brand || 'Unknown Brand', url: `/brands/${(watch.brand || 'unknown').toLowerCase().replace(/\s+/g, '-')}` },
    { name: watch.name || 'Unknown Product', url: `/catalog/${watch.id}` }
  ];

  return (
    <div className="min-h-screen bg-luxury-white">
      <ProductStructuredData product={{
        id: watch.id,
        name: watch.name,
        brand: watch.brand,
        price: watch.price,
        originalPrice: watch.originalPrice,
        images: watch.images,
        category: watch.category,
        description: watch.description,
        specifications: watch.specifications,
        isAvailable: watch.inStock,
        stock: watch.inStock ? 1 : 0,
        rating: watch.rating,
        reviewCount: watch.reviewCount,
        reviews: [],
        relatedProducts: [],
        slug: watch.id,
        tags: []
      }} />
      <BreadcrumbStructuredData items={breadcrumbItems} />

      {/* Breadcrumb */}
      <div className="bg-luxury-cream py-4">
        <div className="container mx-auto px-4">
          <nav className="flex items-center space-x-2 text-sm">
            <Link href="/" className="text-gray-600 hover:text-luxury-gold">
              Home
            </Link>
            <span className="text-gray-400">/</span>
            <Link href="/catalog" className="text-gray-600 hover:text-luxury-gold">
              Catalog
            </Link>
            <span className="text-gray-400">/</span>
            <Link href={`/brands/${(watch.brand || 'unknown').toLowerCase().replace(/\s+/g, '-')}`} className="text-gray-600 hover:text-luxury-gold">
              {watch.brand || 'Unknown Brand'}
            </Link>
            <span className="text-gray-400">/</span>
            <span className="text-luxury-black font-medium">{watch.name}</span>
          </nav>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Image Gallery */}
          <div className="relative">
            <ImageGallery
              images={watch.images}
              productName={`${watch.brand} ${watch.name}`}
            />

            {/* Badges */}
            <div className="absolute top-4 left-4 flex flex-col gap-2 z-10">
              {watch.isNew && (
                <span className="bg-luxury-gold text-luxury-black px-3 py-1 rounded-full text-sm font-semibold">
                  NEW
                </span>
              )}
              {watch.isBestseller && (
                <span className="bg-luxury-charcoal text-white px-3 py-1 rounded-full text-sm font-semibold">
                  BESTSELLER
                </span>
              )}
            </div>
          </div>

          {/* Product Information */}
          <div className="space-y-6">
            {/* Header */}
            <div>
              <p className="text-luxury-gold text-sm font-medium uppercase tracking-wide mb-2">
                {watch.brand || 'Unknown Brand'}
              </p>
              <h1 className="font-luxury-serif text-3xl md:text-4xl font-bold text-luxury-black mb-4">
                {watch.name || 'Unknown Product'}
              </h1>
              
              {/* Rating */}
              <div className="flex items-center gap-2 mb-4">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={cn(
                        "h-4 w-4",
                        i < Math.floor(watch.rating || 0)
                          ? "text-yellow-400 fill-current" 
                          : "text-gray-300"
                      )}
                    />
                  ))}
                </div>
                <span className="text-sm text-gray-600">
                  {watch.rating || 0} ({watch.reviewCount || 0} reviews)
                </span>
              </div>

              {/* Model */}
              <p className="text-sm text-gray-500 mb-4">
                Model: {watch.model}
              </p>
            </div>

            {/* Price */}
            <div className="border-t border-b border-gray-200 py-6">
              <div className="flex items-center gap-4">
                {watch.originalPrice && (
                  <span className="text-gray-400 line-through text-xl">
                    {formatPrice(watch.originalPrice)}
                  </span>
                )}
                <span className="text-3xl font-bold text-luxury-gold font-luxury-serif">
                  {formatPrice(watch.price)}
                </span>
              </div>
              <p className="text-sm text-gray-600 mt-2">
                Price includes VAT. Free shipping on orders over $1,000.
              </p>
            </div>

            {/* Stock Status */}
            <div className="flex items-center gap-2">
              <div className={cn(
                "w-3 h-3 rounded-full",
                watch.inStock ? "bg-green-500" : "bg-red-500"
              )} />
              <span className={cn(
                "font-medium",
                watch.inStock ? "text-green-600" : "text-red-600"
              )}>
                {watch.inStock ? "In Stock" : "Out of Stock"}
              </span>
            </div>

            {/* Actions */}
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <Button
                  onClick={handleAddToCart}
                  disabled={!watch.inStock}
                  className="flex-1 h-12"
                  variant={watch.inStock ? "luxury" : "secondary"}
                >
                  <ShoppingBag className="h-5 w-5 mr-2" />
                  {watch.inStock ? "Add to Cart" : "Out of Stock"}
                </Button>
                
                <Button
                  variant="outline"
                  size="icon"
                  onClick={handleWishlistToggle}
                  className={cn(
                    "h-12 w-12",
                    isInWishlist(watch.id) && "text-red-500 border-red-500"
                  )}
                >
                  <Heart className={cn("h-5 w-5", isInWishlist(watch.id) && "fill-current")} />
                </Button>

                <Button variant="outline" size="icon" className="h-12 w-12">
                  <Share2 className="h-5 w-5" />
                </Button>
              </div>
            </div>

            {/* Features */}
            <div className="bg-luxury-cream rounded-lg p-6">
              <h3 className="font-semibold text-luxury-black mb-4">Key Features</h3>
              <ul className="space-y-2">
                {(watch.features || []).slice(0, 4).map((feature, index) => (
                  <li key={index} className="flex items-center text-sm text-gray-700">
                    <div className="w-2 h-2 bg-luxury-gold rounded-full mr-3" />
                    {feature}
                  </li>
                ))}
              </ul>
            </div>

            {/* Service Icons */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-6">
              <div className="text-center">
                <Truck className="h-8 w-8 text-luxury-gold mx-auto mb-2" />
                <p className="text-xs text-gray-600">Free Shipping</p>
              </div>
              <div className="text-center">
                <Shield className="h-8 w-8 text-luxury-gold mx-auto mb-2" />
                <p className="text-xs text-gray-600">5 Year Warranty</p>
              </div>
              <div className="text-center">
                <RotateCcw className="h-8 w-8 text-luxury-gold mx-auto mb-2" />
                <p className="text-xs text-gray-600">30 Day Returns</p>
              </div>
              <div className="text-center">
                <Award className="h-8 w-8 text-luxury-gold mx-auto mb-2" />
                <p className="text-xs text-gray-600">Authentic</p>
              </div>
            </div>
          </div>
        </div>

        {/* Product Details Tabs */}
        <div className="mt-16">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8">
              {[
                { id: 'description', label: 'Description' },
                { id: 'specifications', label: 'Specifications' },
                { id: 'reviews', label: 'Reviews' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={cn(
                    "py-4 px-1 border-b-2 font-medium text-sm transition-colors",
                    activeTab === tab.id
                      ? "border-luxury-gold text-luxury-gold"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  )}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          <div className="py-8">
            {activeTab === 'description' && (
              <div className="prose max-w-none">
                <p className="text-gray-700 leading-relaxed mb-6">
                  {watch.description || 'No description available.'}
                </p>

                <h3 className="font-luxury-serif text-xl font-semibold text-luxury-black mb-4">
                  Features & Benefits
                </h3>
                <ul className="space-y-3">
                  {(watch.features || []).map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <div className="w-2 h-2 bg-luxury-gold rounded-full mt-2 mr-3 flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {activeTab === 'specifications' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {Object.entries(watch.specifications || {}).map(([key, value]) => (
                  <div key={key} className="flex justify-between py-3 border-b border-gray-100">
                    <span className="font-medium text-gray-900">{key}</span>
                    <span className="text-gray-700">{value}</span>
                  </div>
                ))}
              </div>
            )}

            {activeTab === 'reviews' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="font-luxury-serif text-xl font-semibold text-luxury-black">
                    Customer Reviews
                  </h3>
                  <Button variant="outline">Write a Review</Button>
                </div>

                {/* Review Summary */}
                <div className="bg-luxury-cream rounded-lg p-6">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="text-3xl font-bold text-luxury-black">
                      {watch.rating || 0}
                    </div>
                    <div>
                      <div className="flex items-center mb-1">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={cn(
                              "h-4 w-4",
                              i < Math.floor(watch.rating || 0)
                                ? "text-yellow-400 fill-current"
                                : "text-gray-300"
                            )}
                          />
                        ))}
                      </div>
                      <p className="text-sm text-gray-600">
                        Based on {watch.reviewCount || 0} reviews
                      </p>
                    </div>
                  </div>
                </div>

                {/* Sample Reviews */}
                <div className="space-y-6">
                  {[
                    {
                      id: 1,
                      author: 'James Wilson',
                      rating: 5,
                      date: '2024-01-15',
                      title: 'Exceptional timepiece',
                      content: 'This Submariner exceeded all my expectations. The build quality is outstanding, and the movement is incredibly precise. Worth every penny.'
                    },
                    {
                      id: 2,
                      author: 'Michael Chen',
                      rating: 5,
                      date: '2024-01-10',
                      title: 'Perfect diving companion',
                      content: 'Used this watch on multiple diving trips. The water resistance is excellent, and the bezel action is smooth and precise.'
                    }
                  ].map((review) => (
                    <div key={review.id} className="border-b border-gray-200 pb-6">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-gray-900">{review.author}</span>
                          <div className="flex items-center">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={cn(
                                  "h-3 w-3",
                                  i < review.rating
                                    ? "text-yellow-400 fill-current"
                                    : "text-gray-300"
                                )}
                              />
                            ))}
                          </div>
                        </div>
                        <span className="text-sm text-gray-500">{review.date}</span>
                      </div>
                      <h4 className="font-medium text-gray-900 mb-2">{review.title}</h4>
                      <p className="text-gray-700">{review.content}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Related Products */}
        <div className="mt-16">
          <h2 className="font-luxury-serif text-2xl font-bold text-luxury-black mb-8">
            You Might Also Like
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {getRelatedWatches(watch.id).map((relatedWatch) => (
              <WatchCard key={relatedWatch.id} watch={relatedWatch} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
