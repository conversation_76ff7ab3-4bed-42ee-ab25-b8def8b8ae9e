import { client, serverClient, queries, urlFor } from './client'
import { SimpleWatch } from '@/lib/types'

// Product functions
export async function getAllProducts(): Promise<SimpleWatch[]> {
  try {
    const [products, jewelry] = await Promise.all([
      client.fetch(queries.allProducts),
      client.fetch(queries.allJewelry)
    ])

    const allItems = [
      ...products.map(transformProductToSimpleWatch),
      ...jewelry.map(transformProductToSimpleWatch)
    ]

    // If no products from Sanity, return mock data
    if (allItems.length === 0) {
      return getMockProducts()
    }

    return allItems
  } catch (error) {
    console.error('Error fetching products:', error)
    // Return mock data as fallback
    return getMockProducts()
  }
}

export async function getFeaturedProducts(): Promise<SimpleWatch[]> {
  try {
    const [products, jewelry] = await Promise.all([
      client.fetch(queries.featuredProducts),
      client.fetch(queries.featuredJewelry)
    ])

    const allItems = [
      ...products.map(transformProductToSimpleWatch),
      ...jewelry.map(transformProductToSimpleWatch)
    ]

    // If no featured products from Sanity, return mock featured products
    if (allItems.length === 0) {
      return getMockProducts().filter(product => product.isBestseller || product.isNew)
    }

    return allItems
  } catch (error) {
    console.error('Error fetching featured products:', error)
    // Return mock featured products as fallback
    return getMockProducts().filter(product => product.isBestseller || product.isNew)
  }
}

// Brand functions
export async function getAllBrands() {
  try {
    const brands = await client.fetch(queries.allBrands)
    if (brands.length === 0) {
      return getMockBrands()
    }
    return brands
  } catch (error) {
    console.error('Error fetching brands:', error)
    return getMockBrands()
  }
}

export async function getBrandBySlug(slug: string) {
  try {
    return await client.fetch(queries.brandBySlug, { slug })
  } catch (error) {
    console.error('Error fetching brand by slug:', error)
    return null
  }
}

// Category functions
export async function getAllCategories() {
  try {
    const categories = await client.fetch(queries.allCategories)
    if (categories.length === 0) {
      return getMockCategories()
    }
    return categories
  } catch (error) {
    console.error('Error fetching categories:', error)
    return getMockCategories()
  }
}

// Collection functions
export async function getAllCollections() {
  try {
    return await client.fetch(queries.allCollections)
  } catch (error) {
    console.error('Error fetching collections:', error)
    return []
  }
}

// Review functions
export async function getProductReviews(productId: string) {
  try {
    return await client.fetch(queries.productReviews, { productId })
  } catch (error) {
    console.error('Error fetching product reviews:', error)
    return []
  }
}

// Blog functions
export async function getAllBlogPosts() {
  try {
    return await client.fetch(queries.allBlogPosts)
  } catch (error) {
    console.error('Error fetching blog posts:', error)
    // Return mock data for development
    return getMockBlogPosts()
  }
}

export async function getBlogPostBySlug(slug: string) {
  try {
    return await client.fetch(queries.blogPostBySlug, { slug })
  } catch (error) {
    console.error('Error fetching blog post by slug:', error)
    // Return mock data for development
    return getMockBlogPostBySlug(slug)
  }
}

// Site settings
export async function getSiteSettings() {
  try {
    return await client.fetch(queries.siteSettings)
  } catch (error) {
    console.error('Error fetching site settings:', error)
    return null
  }
}

// Transform functions
function transformProductToSimpleWatch(product: any): SimpleWatch {
  return {
    id: product._id,
    name: product.name,
    brand: product.brand,
    price: product.price,
    originalPrice: product.originalPrice,
    images: product.images?.map((img: any) => urlFor(img).width(400).height(400).url()) || ['/images/placeholder-watch.svg'],
    category: product.category,
    description: product.description || '',
    features: product.features || [],
    isNew: product.isNew || false,
    isBestseller: product.isBestseller || false,
    inStock: product.isAvailable || false
  }
}

// Image URL helper
export function getImageUrl(image: any, width = 400, height = 400) {
  if (!image) return '/images/placeholder-watch.svg'

  // Handle mock data with direct URLs
  if (image.asset?.url) {
    return `${image.asset.url}&w=${width}&h=${height}&fit=crop`
  }

  // Handle Sanity images
  try {
    return urlFor(image).width(width).height(height).url()
  } catch (error) {
    return '/images/placeholder-watch.jpg'
  }
}

// Mock data for development
function getMockBlogPosts() {
  return [
    {
      _id: 'mock-1',
      title: 'Top 5 Luxury Watch Trends for 2025',
      slug: { current: 'luxury-watch-trends-2025' },
      excerpt: 'Discover the most exciting trends shaping the luxury watch industry in 2025, from sustainable materials to smart complications.',
      featuredImage: {
        asset: {
          url: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=800&h=600&fit=crop&crop=center'
        },
        alt: 'Modern luxury watches showcasing 2025 trends'
      },
      author: 'Marcus Timekeeper',
      publishedAt: '2025-06-15T10:00:00Z',
      categories: ['Industry News', 'Trends'],
      tags: ['2025', 'Trends', 'Innovation', 'Sustainability', 'Smart Watches'],
      content: getMockBlogContent('trends-2025')
    },
    {
      _id: 'mock-2',
      title: 'How to Choose Your First Luxury Watch',
      slug: { current: 'choose-first-luxury-watch' },
      excerpt: 'A comprehensive guide for first-time luxury watch buyers, covering everything from budget considerations to brand selection.',
      featuredImage: {
        asset: {
          url: 'https://images.unsplash.com/photo-1594534475808-b18fc33b045e?w=800&h=600&fit=crop&crop=center'
        },
        alt: 'Elegant luxury watch collection for first-time buyers'
      },
      author: 'Elena Horologist',
      publishedAt: '2025-06-12T14:30:00Z',
      categories: ['Buying Guides', 'Beginner'],
      tags: ['First Watch', 'Buying Guide', 'Luxury', 'Investment', 'Beginner'],
      content: getMockBlogContent('first-luxury-watch')
    },
    {
      _id: 'mock-3',
      title: 'The Investment Value of Vintage Timepieces',
      slug: { current: 'vintage-watches-investment-value' },
      excerpt: 'Explore why vintage luxury watches have become one of the most sought-after alternative investments in recent years.',
      featuredImage: {
        asset: {
          url: 'https://images.unsplash.com/photo-1547996160-81dfa63595aa?w=800&h=600&fit=crop&crop=center'
        },
        alt: 'Vintage luxury watches collection showing investment potential'
      },
      author: 'David Chronos',
      publishedAt: '2025-06-10T09:15:00Z',
      categories: ['Investment', 'Vintage'],
      tags: ['Vintage', 'Investment', 'Collectibles', 'Value', 'Market'],
      content: getMockBlogContent('vintage-investment')
    }
  ]
}

// Mock blog content for development
function getMockBlogContent(postType: string) {
  const contentMap = {
    'trends-2025': [
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: 'The luxury watch industry continues to evolve, blending traditional craftsmanship with modern innovation. As we move through 2025, several key trends are reshaping the horological landscape.'
          }
        ]
      },
      {
        _type: 'block',
        style: 'h2',
        children: [
          {
            _type: 'span',
            text: '1. Sustainable Materials Revolution'
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: 'Leading brands are embracing eco-friendly materials without compromising luxury. From recycled precious metals to innovative bio-based straps, sustainability is becoming a hallmark of modern luxury.'
          }
        ]
      },
      {
        _type: 'block',
        style: 'h2',
        children: [
          {
            _type: 'span',
            text: '2. Smart Complications Integration'
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: 'Traditional mechanical movements are being enhanced with subtle smart features, creating hybrid timepieces that respect horological heritage while embracing modern connectivity.'
          }
        ]
      },
      {
        _type: 'block',
        style: 'h2',
        children: [
          {
            _type: 'span',
            text: '3. Vintage-Inspired Designs'
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: 'Nostalgia drives design as brands revisit their archives, creating modern interpretations of classic models that appeal to both collectors and new enthusiasts.'
          }
        ]
      }
    ],
    'first-luxury-watch': [
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: 'Purchasing your first luxury watch is a significant milestone. This investment piece will likely accompany you for decades, making the selection process crucial to your satisfaction and enjoyment.'
          }
        ]
      },
      {
        _type: 'block',
        style: 'h2',
        children: [
          {
            _type: 'span',
            text: 'Setting Your Budget'
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: 'Luxury watches range from $3,000 to over $100,000. For your first piece, consider the $5,000-$15,000 range, which offers excellent quality from respected manufacturers without overwhelming your finances.'
          }
        ]
      },
      {
        _type: 'block',
        style: 'h2',
        children: [
          {
            _type: 'span',
            text: 'Choosing the Right Brand'
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: 'Consider established brands with strong resale value: Rolex, Omega, Tudor, and Breitling offer excellent entry points into luxury watchmaking with proven track records.'
          }
        ]
      },
      {
        _type: 'block',
        style: 'h2',
        children: [
          {
            _type: 'span',
            text: 'Style Considerations'
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: 'Choose a versatile style that works for both professional and casual settings. A classic dress watch or sports watch in stainless steel offers maximum wearability and timeless appeal.'
          }
        ]
      }
    ],
    'vintage-investment': [
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: 'Vintage luxury watches have emerged as one of the most compelling alternative investments, with some pieces appreciating faster than traditional assets like stocks or real estate.'
          }
        ]
      },
      {
        _type: 'block',
        style: 'h2',
        children: [
          {
            _type: 'span',
            text: 'Market Performance'
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: 'The vintage watch market has shown remarkable growth, with certain Rolex models appreciating over 200% in the past decade. Patek Philippe and Audemars Piguet pieces have shown similar trends.'
          }
        ]
      },
      {
        _type: 'block',
        style: 'h2',
        children: [
          {
            _type: 'span',
            text: 'Key Investment Factors'
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: 'Rarity, condition, provenance, and brand heritage are crucial factors. Original papers, boxes, and service history significantly impact value. Limited production runs and discontinued models often perform best.'
          }
        ]
      },
      {
        _type: 'block',
        style: 'h2',
        children: [
          {
            _type: 'span',
            text: 'Investment Strategy'
          }
        ]
      },
      {
        _type: 'block',
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: 'Focus on blue-chip brands with strong market recognition. Buy the best condition you can afford, and consider professional authentication. Think long-term – the best returns come from holding pieces for 5-10 years.'
          }
        ]
      }
    ]
  }

  return (contentMap as Record<string, any>)[postType] || []
}

// Get mock blog post by slug
function getMockBlogPostBySlug(slug: string) {
  const posts = getMockBlogPosts()
  return posts.find(post => post.slug.current === slug) || null
}

// Mock products for development/fallback
function getMockProducts(): SimpleWatch[] {
  return [
    {
      id: 'mock-watch-1',
      name: 'Submariner Date',
      brand: 'Rolex',
      price: 12500,
      originalPrice: 13000,
      images: ['https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop&crop=center'],
      category: 'diving',
      description: 'The Rolex Submariner Date is a legendary diving watch with exceptional water resistance and timeless design.',
      features: ['Water resistant to 300m', 'Automatic movement', 'Ceramic bezel'],
      isNew: false,
      isBestseller: true,
      inStock: true
    },
    {
      id: 'mock-watch-2',
      name: 'Speedmaster Professional',
      brand: 'Omega',
      price: 6500,
      images: ['https://images.unsplash.com/photo-1594534475808-b18fc33b045e?w=400&h=400&fit=crop&crop=center'],
      category: 'chronograph',
      description: 'The legendary Omega Speedmaster Professional, the first watch worn on the moon.',
      features: ['Manual winding', 'Chronograph', 'Hesalite crystal'],
      isNew: false,
      isBestseller: true,
      inStock: true
    },
    {
      id: 'mock-watch-3',
      name: 'Nautilus',
      brand: 'Patek Philippe',
      price: 35000,
      images: ['https://images.unsplash.com/photo-1547996160-81dfa63595aa?w=400&h=400&fit=crop&crop=center'],
      category: 'luxury',
      description: 'The iconic Patek Philippe Nautilus, a masterpiece of luxury sports watch design.',
      features: ['Automatic movement', 'Water resistant', 'Integrated bracelet'],
      isNew: true,
      isBestseller: false,
      inStock: true
    },
    {
      id: 'mock-watch-4',
      name: 'Royal Oak',
      brand: 'Audemars Piguet',
      price: 28000,
      images: ['https://images.unsplash.com/photo-1509048191080-d2e2678e67b4?w=400&h=400&fit=crop&crop=center'],
      category: 'luxury',
      description: 'The revolutionary Audemars Piguet Royal Oak, the first luxury sports watch in steel.',
      features: ['Automatic movement', 'Octagonal bezel', 'Tapisserie dial'],
      isNew: false,
      isBestseller: true,
      inStock: true
    },
    {
      id: 'mock-watch-5',
      name: 'Daytona',
      brand: 'Rolex',
      price: 18500,
      images: ['https://images.unsplash.com/photo-1606390842745-11c6c2d8a5e4?w=400&h=400&fit=crop&crop=center'],
      category: 'chronograph',
      description: 'The Rolex Daytona, the ultimate racing chronograph for professional drivers.',
      features: ['Automatic chronograph', 'Tachymeter bezel', 'Ceramic bezel'],
      isNew: true,
      isBestseller: true,
      inStock: false
    },
    {
      id: 'mock-jewelry-1',
      name: 'Diamond Tennis Bracelet',
      brand: 'Cartier',
      price: 15000,
      images: ['https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=400&h=400&fit=crop&crop=center'],
      category: 'jewelry',
      description: 'Elegant diamond tennis bracelet crafted in 18k white gold with brilliant-cut diamonds.',
      features: ['18k white gold', '5 carats total weight', 'Secure clasp'],
      isNew: false,
      isBestseller: true,
      inStock: true
    },
    {
      id: 'mock-jewelry-2',
      name: 'Solitaire Diamond Ring',
      brand: 'Tiffany & Co.',
      price: 8500,
      images: ['https://images.unsplash.com/photo-1605100804763-247f67b3557e?w=400&h=400&fit=crop&crop=center'],
      category: 'jewelry',
      description: 'Classic solitaire engagement ring featuring a brilliant-cut diamond in platinum setting.',
      features: ['Platinum setting', '1 carat diamond', 'Certified GIA'],
      isNew: true,
      isBestseller: true,
      inStock: true
    },
    {
      id: 'mock-jewelry-3',
      name: 'Pearl Necklace',
      brand: 'Mikimoto',
      price: 3500,
      images: ['https://images.unsplash.com/photo-1506630448388-4e683c67ddb0?w=400&h=400&fit=crop&crop=center'],
      category: 'jewelry',
      description: 'Lustrous Akoya pearl necklace with 18k gold clasp, a timeless classic.',
      features: ['Akoya pearls', '18k gold clasp', '18-inch length'],
      isNew: false,
      isBestseller: false,
      inStock: true
    }
  ]
}

// Mock brands for development/fallback
function getMockBrands() {
  return [
    { _id: 'mock-brand-1', name: 'Rolex', slug: { current: 'rolex' }, description: 'Swiss luxury watch manufacturer', isLuxury: true, priceRange: 'high' },
    { _id: 'mock-brand-2', name: 'Omega', slug: { current: 'omega' }, description: 'Swiss luxury watch brand', isLuxury: true, priceRange: 'medium' },
    { _id: 'mock-brand-3', name: 'Patek Philippe', slug: { current: 'patek-philippe' }, description: 'Swiss luxury watch manufacturer', isLuxury: true, priceRange: 'ultra-high' },
    { _id: 'mock-brand-4', name: 'Audemars Piguet', slug: { current: 'audemars-piguet' }, description: 'Swiss luxury watch manufacturer', isLuxury: true, priceRange: 'ultra-high' },
    { _id: 'mock-brand-5', name: 'Cartier', slug: { current: 'cartier' }, description: 'French luxury jewelry and watch brand', isLuxury: true, priceRange: 'high' },
    { _id: 'mock-brand-6', name: 'Tiffany & Co.', slug: { current: 'tiffany-co' }, description: 'American luxury jewelry brand', isLuxury: true, priceRange: 'high' },
    { _id: 'mock-brand-7', name: 'Mikimoto', slug: { current: 'mikimoto' }, description: 'Japanese luxury pearl jewelry brand', isLuxury: true, priceRange: 'medium' }
  ]
}

// Mock categories for development/fallback
function getMockCategories() {
  return [
    { _id: 'mock-cat-1', name: 'Diving Watches', slug: { current: 'diving' }, description: 'Professional diving timepieces' },
    { _id: 'mock-cat-2', name: 'Chronographs', slug: { current: 'chronograph' }, description: 'Precision timing instruments' },
    { _id: 'mock-cat-3', name: 'Luxury Watches', slug: { current: 'luxury' }, description: 'Premium luxury timepieces' },
    { _id: 'mock-cat-4', name: 'Jewelry', slug: { current: 'jewelry' }, description: 'Fine jewelry and accessories' }
  ]
}
